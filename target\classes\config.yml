# AcePhantomBlocker 配置文件
# 作者: AceBrand
# 版本: 1.0.0

# 是否启用插件功能
enabled: true

# 怪物类型控制
# 是否阻止幻翼(Phantom)生成
block-phantom: true

# 是否阻止恼鬼(Vex)生成
block-vex: true

# 生成方式控制
# 是否阻止自然生成
# - 幻翼(Phantom): 玩家连续3天以上不睡觉时在夜晚自然生成
# - 恼鬼(Vex): 通常不自然生成，主要由唤魔者召唤
block-natural-spawn: true

# 是否阻止从刷怪笼/刷怪蛋生成
# - 包括玩家使用刷怪蛋和刷怪笼生成
block-spawner-spawn: true

# 是否阻止通过命令生成
# - 包括 /summon 命令等
block-command-spawn: false

# 权限控制
# 是否允许具有绕过权限的玩家附近生成怪物
# 权限节点: acephantomblocker.bypass
allow-bypass-permission: true

# 调试模式
# 是否启用调试模式（在控制台输出阻止怪物生成的信息）
debug: false

# 配置文件版本（请勿修改）
config-version: 1
