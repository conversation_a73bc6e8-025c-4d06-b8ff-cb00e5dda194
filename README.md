# AcePhantomBlocker

一个用于阻止幻翼(Phantom)和恼鬼(Vex)生成的 Minecraft 1.20.4 插件。

## 功能特性

- 🚫 阻止幻翼(Phantom)自然生成
- 🚫 阻止恼鬼(Vex)生成
- ⚙️ 可配置的开关控制
- 🔧 支持多种生成方式的独立控制
- 🥚 阻止刷怪蛋使用
- 👑 管理员命令支持
- 🛡️ 权限系统支持
- 🔄 热重载配置

## 安装方法

1. 下载插件 jar 文件
2. 将文件放入服务器的 `plugins` 文件夹
3. 重启服务器或使用 `/reload` 命令
4. 编辑 `plugins/AcePhantomBlocker/config.yml` 配置文件

## 配置说明

```yaml
# 是否启用插件功能
enabled: true

# 是否阻止恼鬼自然生成（玩家长时间未睡觉导致的生成）
block-natural-spawn: true

# 是否阻止恼鬼从刷怪笼生成
block-spawner-spawn: true

# 是否阻止恼鬼通过命令生成
block-command-spawn: false

# 是否允许具有绕过权限的玩家附近生成恼鬼
allow-bypass-permission: true

# 是否启用调试模式
debug: false
```

## 命令使用

主命令: `/acephantom` (别名: `/aphantom`, `/apb`)

- `/acephantom reload` - 重载配置文件
- `/acephantom toggle` - 切换插件启用状态
- `/acephantom status` - 查看插件状态
- `/acephantom help` - 显示帮助信息

## 权限节点

- `acephantomblocker.admin` - 允许使用所有管理命令 (默认: op)
- `acephantomblocker.bypass` - 允许恼鬼在此玩家附近生成 (默认: false)

## 编译方法

确保你已安装 Java 17 和 Maven，然后运行：

```bash
mvn clean package
```

编译后的插件文件将在 `target` 文件夹中。

## 技术信息

- **Minecraft 版本**: 1.20.4
- **API**: Spigot/Paper
- **Java 版本**: 17+
- **包名**: cn.acebrand.acephantomblocker

## 作者

AceBrand - https://acebrand.cn

## 许可证

此项目使用 MIT 许可证。
