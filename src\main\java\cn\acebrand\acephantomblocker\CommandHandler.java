package cn.acebrand.acephantomblocker;

import org.bukkit.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

public class CommandHandler {
    
    private final AcePhantomBlocker plugin;
    
    public CommandHandler(AcePhantomBlocker plugin) {
        this.plugin = plugin;
    }
    
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!command.getName().equalsIgnoreCase("acephantom")) {
            return false;
        }
        
        // 检查权限
        if (!sender.hasPermission("acephantomblocker.admin")) {
            sender.sendMessage(ChatColor.RED + "你没有权限使用此命令!");
            return true;
        }
        
        if (args.length == 0) {
            sendHelpMessage(sender);
            return true;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "reload":
                handleReload(sender);
                break;
            case "toggle":
                handleToggle(sender);
                break;
            case "status":
                handleStatus(sender);
                break;
            case "help":
                sendHelpMessage(sender);
                break;
            default:
                sender.sendMessage(ChatColor.RED + "未知的子命令: " + subCommand);
                sendHelpMessage(sender);
                break;
        }
        
        return true;
    }
    
    private void handleReload(CommandSender sender) {
        try {
            plugin.reloadPluginConfig();
            sender.sendMessage(ChatColor.GREEN + "AcePhantomBlocker 配置已重载!");
            sender.sendMessage(ChatColor.YELLOW + "当前状态: " + 
                (plugin.isPluginEnabled() ? ChatColor.GREEN + "启用" : ChatColor.RED + "禁用"));
        } catch (Exception e) {
            sender.sendMessage(ChatColor.RED + "重载配置时发生错误: " + e.getMessage());
            plugin.getLogger().warning("重载配置时发生错误: " + e.getMessage());
        }
    }
    
    private void handleToggle(CommandSender sender) {
        boolean oldState = plugin.isPluginEnabled();
        plugin.togglePlugin();
        boolean newState = plugin.isPluginEnabled();
        
        String status = newState ? ChatColor.GREEN + "启用" : ChatColor.RED + "禁用";
        sender.sendMessage(ChatColor.YELLOW + "AcePhantomBlocker 已" + status);
        
        if (newState != oldState) {
            String action = newState ? "启用" : "禁用";
            plugin.getLogger().info("插件状态已被 " + sender.getName() + " " + action);
        }
    }
    
    private void handleStatus(CommandSender sender) {
        boolean enabled = plugin.isPluginEnabled();
        String status = enabled ? ChatColor.GREEN + "启用" : ChatColor.RED + "禁用";
        
        sender.sendMessage(ChatColor.GOLD + "=== AcePhantomBlocker 状态 ===");
        sender.sendMessage(ChatColor.YELLOW + "插件状态: " + status);
        sender.sendMessage(ChatColor.YELLOW + "版本: " + ChatColor.WHITE + plugin.getDescription().getVersion());
        sender.sendMessage(ChatColor.YELLOW + "作者: " + ChatColor.WHITE + plugin.getDescription().getAuthors());
        
        if (enabled) {
            boolean blockNatural = plugin.getConfig().getBoolean("block-natural-spawn", true);
            boolean blockSpawner = plugin.getConfig().getBoolean("block-spawner-spawn", true);
            boolean blockCommand = plugin.getConfig().getBoolean("block-command-spawn", false);
            boolean allowBypass = plugin.getConfig().getBoolean("allow-bypass-permission", true);
            
            sender.sendMessage(ChatColor.YELLOW + "阻止自然生成: " + 
                (blockNatural ? ChatColor.GREEN + "是" : ChatColor.RED + "否"));
            sender.sendMessage(ChatColor.YELLOW + "阻止刷怪笼生成: " + 
                (blockSpawner ? ChatColor.GREEN + "是" : ChatColor.RED + "否"));
            sender.sendMessage(ChatColor.YELLOW + "阻止命令生成: " + 
                (blockCommand ? ChatColor.GREEN + "是" : ChatColor.RED + "否"));
            sender.sendMessage(ChatColor.YELLOW + "允许绕过权限: " + 
                (allowBypass ? ChatColor.GREEN + "是" : ChatColor.RED + "否"));
        }
    }
    
    private void sendHelpMessage(CommandSender sender) {
        sender.sendMessage(ChatColor.GOLD + "=== AcePhantomBlocker 帮助 ===");
        sender.sendMessage(ChatColor.YELLOW + "/acephantom reload" + ChatColor.WHITE + " - 重载配置文件");
        sender.sendMessage(ChatColor.YELLOW + "/acephantom toggle" + ChatColor.WHITE + " - 切换插件启用状态");
        sender.sendMessage(ChatColor.YELLOW + "/acephantom status" + ChatColor.WHITE + " - 查看插件状态");
        sender.sendMessage(ChatColor.YELLOW + "/acephantom help" + ChatColor.WHITE + " - 显示此帮助信息");
    }
}
