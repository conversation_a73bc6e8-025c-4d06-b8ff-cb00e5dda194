name: AcePhantomBlocker
version: 1.0.0
main: cn.acebrand.acephantomblocker.AcePhantomBlocker
api-version: 1.20
author: AceBrand
description: A plugin to prevent phantom spawning with configurable settings
website: https://acebrand.cn

commands:
  acephantom:
    description: Main command for AcePhantomBlocker
    usage: /acephantom <reload|toggle|status>
    permission: acephantomblocker.admin
    aliases: [aphantom, apb]

permissions:
  acephantomblocker.admin:
    description: Allows access to all AcePhantomBlocker commands
    default: op
  acephantomblocker.bypass:
    description: Allows phantoms to spawn for this player (if enabled)
    default: false
