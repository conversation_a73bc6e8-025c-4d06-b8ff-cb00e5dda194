package cn.acebrand.acephantomblocker;

import org.bukkit.plugin.java.JavaPlugin;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.event.Listener;
import org.bukkit.command.Command;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.ChatColor;

public class AcePhantomBlocker extends JavaPlugin implements Listener {
    
    private PhantomListener phantomListener;
    private CommandHandler commandHandler;
    
    @Override
    public void onEnable() {
        // 保存默认配置文件
        saveDefaultConfig();
        
        // 初始化监听器
        phantomListener = new PhantomListener(this);
        getServer().getPluginManager().registerEvents(phantomListener, this);
        
        // 初始化命令处理器
        commandHandler = new CommandHandler(this);
        
        getLogger().info("AcePhantomBlocker 插件已启用!");
        getLogger().info("恼鬼生成阻止功能: " + (getConfig().getBoolean("enabled") ? "开启" : "关闭"));
    }
    
    @Override
    public void onDisable() {
        getLogger().info("AcePhantomBlocker 插件已禁用!");
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        return commandHandler.onCommand(sender, command, label, args);
    }
    
    /**
     * 重载配置文件
     */
    public void reloadPluginConfig() {
        reloadConfig();
        phantomListener.updateConfig();
    }
    
    /**
     * 切换插件启用状态
     */
    public void togglePlugin() {
        FileConfiguration config = getConfig();
        boolean currentState = config.getBoolean("enabled");
        config.set("enabled", !currentState);
        saveConfig();
        phantomListener.updateConfig();
    }
    
    /**
     * 获取插件状态
     */
    public boolean isPluginEnabled() {
        return getConfig().getBoolean("enabled");
    }
}
