package cn.acebrand.acephantomblocker;

import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.CreatureSpawnEvent;
import org.bukkit.event.entity.CreatureSpawnEvent.SpawnReason;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.Material;
import org.bukkit.ChatColor;

public class PhantomListener implements Listener {
    
    private final AcePhantomBlocker plugin;
    private boolean enabled;
    private boolean blockNaturalSpawn;
    private boolean blockSpawnerSpawn;
    private boolean blockCommandSpawn;
    private boolean allowBypassPermission;
    private boolean blockVex;
    private boolean blockPhantom;
    
    public PhantomListener(AcePhantomBlocker plugin) {
        this.plugin = plugin;
        updateConfig();
    }
    
    /**
     * 更新配置
     */
    public void updateConfig() {
        enabled = plugin.getConfig().getBoolean("enabled", true);
        blockNaturalSpawn = plugin.getConfig().getBoolean("block-natural-spawn", true);
        blockSpawnerSpawn = plugin.getConfig().getBoolean("block-spawner-spawn", true);
        blockCommandSpawn = plugin.getConfig().getBoolean("block-command-spawn", false);
        allowBypassPermission = plugin.getConfig().getBoolean("allow-bypass-permission", true);
        blockVex = plugin.getConfig().getBoolean("block-vex", true);
        blockPhantom = plugin.getConfig().getBoolean("block-phantom", true);
    }
    
    @EventHandler(priority = EventPriority.HIGH)
    public void onCreatureSpawn(CreatureSpawnEvent event) {
        // 如果插件未启用，直接返回
        if (!enabled) {
            return;
        }

        // 检查是否是我们要阻止的怪物
        boolean isPhantom = event.getEntityType() == EntityType.PHANTOM;
        boolean isVex = event.getEntityType() == EntityType.VEX;

        if (!isPhantom && !isVex) {
            return;
        }

        // 检查是否应该阻止这种怪物
        if (isPhantom && !blockPhantom) {
            return;
        }
        if (isVex && !blockVex) {
            return;
        }
        
        SpawnReason reason = event.getSpawnReason();
        boolean shouldBlock = false;
        
        // 根据生成原因决定是否阻止
        switch (reason) {
            case NATURAL:
                // 自然生成：幻翼由玩家不睡觉触发，恼鬼通常不自然生成
                shouldBlock = blockNaturalSpawn;
                break;
            case SPAWNER:
            case SPAWNER_EGG:
                // 刷怪笼/刷怪蛋生成
                shouldBlock = blockSpawnerSpawn;
                break;
            case COMMAND:
            case CUSTOM:
                // 命令/自定义生成
                shouldBlock = blockCommandSpawn;
                break;
            case JOCKEY:
            case CHUNK_GEN:
            case BUILD_IRONGOLEM:
            case BUILD_SNOWMAN:
            case BUILD_WITHER:
            case VILLAGE_DEFENSE:
            case VILLAGE_INVASION:
            case BREEDING:
            case SLIME_SPLIT:
            case REINFORCEMENTS:
            case NETHER_PORTAL:
            case DISPENSE_EGG:
            case EGG:
            case SILVERFISH_BLOCK:
            case MOUNT:
            case TRAP:
            case ENDER_PEARL:
                // 其他特殊生成原因，根据配置决定
                shouldBlock = blockSpawnerSpawn; // 大部分归类为非自然生成
                break;
            default:
                // 未知原因默认阻止
                shouldBlock = true;
                break;
        }
        
        if (shouldBlock) {
            // 检查绕过权限（主要针对自然生成）
            if (allowBypassPermission && reason == SpawnReason.NATURAL) {
                // 检查附近是否有具有绕过权限的玩家
                boolean hasBypassPlayer = event.getLocation().getWorld()
                    .getNearbyEntities(event.getLocation(), 128, 128, 128)
                    .stream()
                    .filter(entity -> entity instanceof Player)
                    .map(entity -> (Player) entity)
                    .anyMatch(player -> player.hasPermission("acephantomblocker.bypass"));

                if (hasBypassPlayer) {
                    // 调试信息
                    if (plugin.getConfig().getBoolean("debug", false)) {
                        String mobType = isPhantom ? "幻翼(Phantom)" : "恼鬼(Vex)";
                        plugin.getLogger().info("允许" + mobType + "生成 - 附近有绕过权限的玩家");
                    }
                    return; // 允许生成
                }
            }
            
            // 阻止恼鬼生成
            event.setCancelled(true);
            
            // 调试信息（可选）
            if (plugin.getConfig().getBoolean("debug", false)) {
                String mobType = isPhantom ? "幻翼(Phantom)" : "恼鬼(Vex)";
                plugin.getLogger().info("阻止了" + mobType + "生成 - 原因: " + reason.name() +
                    " 位置: " + event.getLocation().toString());
            }
        }
    }

    @EventHandler(priority = EventPriority.HIGH)
    public void onPlayerUseSpawnEgg(PlayerInteractEvent event) {
        // 如果插件未启用，直接返回
        if (!enabled) {
            return;
        }

        // 检查是否是我们要阻止的刷怪蛋
        if (event.getItem() != null) {
            boolean isPhantomEgg = event.getItem().getType() == Material.PHANTOM_SPAWN_EGG;
            boolean isVexEgg = event.getItem().getType() == Material.VEX_SPAWN_EGG;

            if (isPhantomEgg || isVexEgg) {
                // 检查是否应该阻止这种刷怪蛋
                boolean shouldBlock = false;
                String eggType = "";

                if (isPhantomEgg && blockPhantom && blockSpawnerSpawn) {
                    shouldBlock = true;
                    eggType = "幻翼刷怪蛋";
                } else if (isVexEgg && blockVex && blockSpawnerSpawn) {
                    shouldBlock = true;
                    eggType = "恼鬼刷怪蛋";
                }

                if (shouldBlock) {
                    // 检查绕过权限
                    if (allowBypassPermission && event.getPlayer().hasPermission("acephantomblocker.bypass")) {
                        return; // 允许使用
                    }

                    // 阻止使用刷怪蛋
                    event.setCancelled(true);
                    event.getPlayer().sendMessage(ChatColor.RED + eggType + "已被禁用!");

                    // 调试信息
                    if (plugin.getConfig().getBoolean("debug", false)) {
                        plugin.getLogger().info("阻止了玩家 " + event.getPlayer().getName() +
                            " 使用" + eggType);
                    }
                }
            }
        }
    }
}
