package cn.acebrand.acephantomblocker;

import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.CreatureSpawnEvent;
import org.bukkit.event.entity.CreatureSpawnEvent.SpawnReason;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.Material;
import org.bukkit.ChatColor;

public class PhantomListener implements Listener {
    
    private final AcePhantomBlocker plugin;
    private boolean enabled;
    private boolean blockNaturalSpawn;
    private boolean blockSpawnerSpawn;
    private boolean blockCommandSpawn;
    private boolean allowBypassPermission;
    
    public PhantomListener(AcePhantomBlocker plugin) {
        this.plugin = plugin;
        updateConfig();
    }
    
    /**
     * 更新配置
     */
    public void updateConfig() {
        enabled = plugin.getConfig().getBoolean("enabled", true);
        blockNaturalSpawn = plugin.getConfig().getBoolean("block-natural-spawn", true);
        blockSpawnerSpawn = plugin.getConfig().getBoolean("block-spawner-spawn", true);
        blockCommandSpawn = plugin.getConfig().getBoolean("block-command-spawn", false);
        allowBypassPermission = plugin.getConfig().getBoolean("allow-bypass-permission", true);
    }
    
    @EventHandler(priority = EventPriority.HIGH)
    public void onPhantomSpawn(CreatureSpawnEvent event) {
        // 如果插件未启用，直接返回
        if (!enabled) {
            return;
        }
        
        // 检查是否是恼鬼
        if (event.getEntityType() != EntityType.PHANTOM) {
            return;
        }
        
        SpawnReason reason = event.getSpawnReason();
        boolean shouldBlock = false;
        
        // 根据生成原因决定是否阻止
        switch (reason) {
            case NATURAL:
                shouldBlock = blockNaturalSpawn;
                break;
            case SPAWNER:
            case SPAWNER_EGG:
                shouldBlock = blockSpawnerSpawn;
                break;
            case COMMAND:
            case CUSTOM:
                shouldBlock = blockCommandSpawn;
                break;
            default:
                // 其他原因默认阻止
                shouldBlock = true;
                break;
        }
        
        if (shouldBlock) {
            // 检查绕过权限
            if (allowBypassPermission && reason == SpawnReason.NATURAL) {
                // 检查附近是否有具有绕过权限的玩家
                boolean hasBypassPlayer = event.getLocation().getWorld()
                    .getNearbyEntities(event.getLocation(), 128, 128, 128)
                    .stream()
                    .filter(entity -> entity instanceof Player)
                    .map(entity -> (Player) entity)
                    .anyMatch(player -> player.hasPermission("acephantomblocker.bypass"));
                
                if (hasBypassPlayer) {
                    return; // 允许生成
                }
            }
            
            // 阻止恼鬼生成
            event.setCancelled(true);
            
            // 调试信息（可选）
            if (plugin.getConfig().getBoolean("debug", false)) {
                plugin.getLogger().info("阻止了恼鬼生成 - 原因: " + reason.name() +
                    " 位置: " + event.getLocation().toString());
            }
        }
    }

    @EventHandler(priority = EventPriority.HIGH)
    public void onPlayerUseSpawnEgg(PlayerInteractEvent event) {
        // 如果插件未启用，直接返回
        if (!enabled) {
            return;
        }

        // 检查是否是恼鬼刷怪蛋
        if (event.getItem() != null && event.getItem().getType() == Material.PHANTOM_SPAWN_EGG) {
            // 检查是否应该阻止刷怪蛋使用
            if (blockSpawnerSpawn) {
                // 检查绕过权限
                if (allowBypassPermission && event.getPlayer().hasPermission("acephantomblocker.bypass")) {
                    return; // 允许使用
                }

                // 阻止使用刷怪蛋
                event.setCancelled(true);
                event.getPlayer().sendMessage(ChatColor.RED + "恼鬼刷怪蛋已被禁用!");

                // 调试信息
                if (plugin.getConfig().getBoolean("debug", false)) {
                    plugin.getLogger().info("阻止了玩家 " + event.getPlayer().getName() +
                        " 使用恼鬼刷怪蛋");
                }
            }
        }
    }
}
